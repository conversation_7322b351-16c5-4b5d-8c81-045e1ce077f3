"use client";
import React, { useState } from "react";
import { AlertNotificationSettings } from "@/types/systemSettingsTypes";
import SettingsToggle from "../SettingsToggle";
import { AiOutlinePlus } from "react-icons/ai";
import EmergencyContactModal from "./EmergencyContactModal";

interface AlertNotificationSectionProps {
  data: AlertNotificationSettings;
  errors: Partial<AlertNotificationSettings>;
  onChange: (field: keyof AlertNotificationSettings, value: boolean) => void;
  onSave: () => void;
  onReset: () => void;
  loading: boolean;
  hasChanges: boolean;
}

interface NotificationItem {
  id: keyof AlertNotificationSettings;
  title: string;
  description: string;
}

interface AddButtonItem {
  label: string;
  onClick?: () => void;
}

// Reusable Components
const NotificationToggleItem: React.FC<{
  title: string;
  description: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  error?: string;
  id: string;
}> = ({ title, description, checked, onChange, error, id }) => (
  <div className="flex items-start justify-between">
    <div className="flex-1">
      <h4 className="text-white font-medium mb-2">{title}</h4>
      <p className="text-[#A3A3A3] text-xs line-clamp-2">{description}</p>
    </div>
    <div className="ml-6">
      <SettingsToggle
        id={id}
        label=""
        description=""
        checked={checked}
        onChange={onChange}
        error={error || ""}
      />
    </div>
  </div>
);

const AddButton: React.FC<{
  label: string;
  onClick?: () => void;
}> = ({ label, onClick }) => (
  <button
    type="button"
    onClick={onClick}
    className="px-7 py-2 cursor-pointer border border-[#A3A3A3] text-[#A3A3A3] text-sm flex items-center gap-2"
  >
    <AiOutlinePlus size={16} />
    {label}
  </button>
);

const SectionHeader: React.FC<{
  title: string;
  description?: string;
}> = ({ title, description }) => (
  <div className="border-b border-[#3D3D3D] pb-4">
    <h2 className="text-xl text-white font-medium">{title}</h2>
    {description && (
      <p className="text-[#A3A3A3] text-sm mt-1">{description}</p>
    )}
  </div>
);

const AlertNotificationSection: React.FC<AlertNotificationSectionProps> = ({
  data,
  errors,
  onChange,
}) => {
  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<"primary" | "backup">("primary");

  // Modal handlers
  const handleAddContact = (type: "primary" | "backup") => {
    setModalType(type);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleContactAdded = () => {
    setIsModalOpen(false);
    // Here you would typically refresh the data or update the state
  };

  // Configuration data
  const generalNotifications: NotificationItem[] = [
    {
      id: "emailNotifications",
      title: "Activity Alerts",
      description:
        "Get notified for door access logs, face recognition events, or device status changes.",
    },
    {
      id: "systemUpdates",
      title: "System Health Updates",
      description:
        "Stay updated when cameras go offline or security devices fail.",
    },
  ];

  const emergencyItems: AddButtonItem[] = [
    {
      label: "Primary Emergency Contact",
      onClick: () => handleAddContact("primary")
    },
    {
      label: "Backup Emergency Line",
      onClick: () => handleAddContact("backup")
    },
  ];

  return (
    <div className="space-y-8">
      <SectionHeader
        title="Alert & Notification Settings"
        description="Customize how your system communicates security events, alerts, and escalations."
      />

      {/* General Notifications Section */}
      <div className="border-b border-[#3D3D3D] pb-8">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg text-white font-medium">
              General Notifications
            </h3>
          </div>

          <div className="flex-1 space-y-6">
            {generalNotifications.map((item) => (
              <NotificationToggleItem
                key={item.id}
                id={item.id}
                title={item.title}
                description={item.description}
                checked={data[item.id] as boolean}
                onChange={(checked) => onChange(item.id, checked)}
                error={errors[item.id] ? String(errors[item.id]) : ""}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Critical Security Alerts Section */}
      <div className="border-b border-[#3D3D3D] pb-8">
        <div className="flex items-center justify-between">
          <h3 className="text-lg text-white font-medium">
            Critical Security Alerts
          </h3>
          <div
            className="text-white py-4 px-10 w-fit"
            style={{
              background:
                "linear-gradient(145.42deg, rgba(31, 31, 31, 0.5) 32.16%, rgba(112, 112, 112, 0.5) 237.44%)",
            }}
          >
            <span>Always Active</span>
          </div>
        </div>
      </div>

      {/* Emergency Alert Setup Section */}
      <div>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg text-white font-medium">
              Emergency Alert Setup
            </h3>
          </div>

          <div className="flex-1 space-y-6">
            {emergencyItems.map((item) => (
              <div
                key={item.label}
                className="flex items-center justify-between"
              >
                <span className="text-white font-medium">{item.label}</span>
                <AddButton label="Add" onClick={item.onClick} />
              </div>
            ))}

            <NotificationToggleItem
              id="panicAlertButton"
              title="Panic Alert Button"
              description="Trigger emergency alert and notify responders"
              checked={data.securityAlerts}
              onChange={(checked) => onChange("securityAlerts", checked)}
              error={errors.securityAlerts ? String(errors.securityAlerts) : ""}
            />
          </div>
        </div>
      </div>

      {/* Emergency Contact Modal */}
      <EmergencyContactModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onContactAdded={handleContactAdded}
        contactType={modalType}
      />
    </div>
  );
};

export default AlertNotificationSection;
