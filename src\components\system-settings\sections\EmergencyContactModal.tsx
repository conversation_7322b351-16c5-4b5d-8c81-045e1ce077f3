"use client";
import React, { useState } from "react";
import { DetailedModal } from "@/components/Modal";
import EmergencyContactForm from "./EmergencyContactForm";
import EmergencyContactSuccessModal from "./EmergencyContactSuccessModal";
import { EmergencyContactFormData } from "./hooks";

interface EmergencyContactModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContactAdded: () => void;
  contactType: "primary" | "backup";
}

const EmergencyContactModal: React.FC<EmergencyContactModalProps> = ({
  isOpen,
  onClose,
  onContactAdded,
  contactType,
}) => {
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const handleFormSubmit = async (formData: EmergencyContactFormData) => {
    try {
      // Here you would typically make an API call to save the emergency contact
      console.log("Emergency contact data:", formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success modal
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error saving emergency contact:", error);
      // Handle error - could show error toast here
    }
  };

  const handleModalClose = () => {
    setShowSuccessModal(false);
    onClose();
  };

  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    onContactAdded();
  };

  const getTitle = () => {
    return contactType === "primary" 
      ? "Add Emergency Number" 
      : "Add Emergency Number";
  };

  const getDescription = () => {
    return contactType === "primary"
      ? "Add your primary emergency contact for critical alerts"
      : "Add a backup emergency contact for redundancy";
  };

  return (
    <>
      <DetailedModal isOpen={isOpen && !showSuccessModal} onClose={handleModalClose}>
        <div className="w-full">
          <div className="mb-6 border-b border-[#3D3D3D] pb-4">
            <h2 className="text-lg lg:text-xl text-white font-medium mb-2">
              {getTitle()}
            </h2>
            <p className="text-sm text-[#A3A3A3]">
              {getDescription()}
            </p>
          </div>

          <EmergencyContactForm
            onSubmit={handleFormSubmit}
            contactType={contactType}
          />
        </div>
      </DetailedModal>

      <EmergencyContactSuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessClose}
      />
    </>
  );
};

export default EmergencyContactModal;
