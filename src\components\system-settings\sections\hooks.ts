import { useState, useCallback } from "react";

export interface EmergencyContactFormData {
  phoneNumber: string;
  type: string;
}

export interface EmergencyContactFormErrors {
  phoneNumber: string;
  type: string;
}

export const useEmergencyContactForm = (
  onSubmit: (formData: EmergencyContactFormData) => Promise<void>
) => {
  const [formData, setFormData] = useState<EmergencyContactFormData>({
    phoneNumber: "",
    type: "",
  });

  const [errors, setErrors] = useState<EmergencyContactFormErrors>({
    phoneNumber: "",
    type: "",
  });

  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  // Validation functions
  const validatePhoneNumber = useCallback((phone: string): boolean => {
    if (!phone.trim()) return false;
    // Basic phone number validation - allows various formats
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, "");
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
  }, []);

  const validateType = useCallback((type: string): boolean => {
    return type.trim() !== "";
  }, []);

  const validateField = useCallback((field: keyof EmergencyContactFormData, value: string) => {
    switch (field) {
      case "phoneNumber":
        if (!value.trim()) return "Phone number is required";
        return validatePhoneNumber(value) ? "" : "Please enter a valid phone number";
      case "type":
        return validateType(value) ? "" : "Please select a contact type";
      default:
        return "";
    }
  }, [validatePhoneNumber, validateType]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    const field = id as keyof EmergencyContactFormData;
    
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Real-time validation
    const error = validateField(field, value);
    setErrors(prev => ({ ...prev, [field]: error }));
  }, [validateField]);

  const handleTypeChange = useCallback((type: string) => {
    setFormData(prev => ({ ...prev, type }));
    
    // Real-time validation for type
    const error = validateField("type", type);
    setErrors(prev => ({ ...prev, type: error }));
  }, [validateField]);

  const handleFocus = useCallback((field: string) => {
    setFocusedField(field);
  }, []);

  const handleBlur = useCallback(() => {
    setFocusedField(null);
  }, []);

  const isFormValid = useCallback(() => {
    const phoneValid = validatePhoneNumber(formData.phoneNumber);
    const typeValid = validateType(formData.type);
    return phoneValid && typeValid;
  }, [formData, validatePhoneNumber, validateType]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);

    // Validate all fields
    const newErrors: EmergencyContactFormErrors = {
      phoneNumber: validateField("phoneNumber", formData.phoneNumber),
      type: validateField("type", formData.type),
    };

    setErrors(newErrors);

    // Check if form is valid
    if (Object.values(newErrors).every(error => error === "")) {
      await onSubmit(formData);
    }
  }, [formData, validateField, onSubmit]);

  const resetForm = useCallback(() => {
    setFormData({
      phoneNumber: "",
      type: "",
    });
    setErrors({
      phoneNumber: "",
      type: "",
    });
    setFocusedField(null);
    setSubmitAttempted(false);
  }, []);

  return {
    formData,
    errors,
    focusedField,
    submitAttempted,
    handleChange,
    handleTypeChange,
    handleFocus,
    handleBlur,
    handleSubmit,
    isFormValid: isFormValid(),
    resetForm,
  };
};
